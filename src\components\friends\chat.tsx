"use client";

import { User } from "better-auth";
import React from "react";
import { io, Socket } from "socket.io-client";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Textarea } from "../ui/textarea";

let socket: Socket | null = null;

export default ({
  me,
  you,
  chatOpen,
}: {
  me: User;
  you: Omit<User, "image" | "email" | "emailVerified" | "createdAt" | "updatedAt" | "name">;
  chatOpen: boolean;
}) => {
  const [message, setMessage] = React.useState<string>("");
  const [chatLog, setChatLog] = React.useState<string[]>([]);

  React.useEffect(() => {
    if (!socket) {
      socket = io(process.env.NEXT_PUBLIC_MESSAGE_SOCKET_URL!, {
        withCredentials: true,
        autoConnect: true,
      });

      socket.on("connect", () => {
        console.log("Connected to socket server");
        socket?.emit("register", { userId: me.id });
      });

      socket.on("disconnect", () => {
        console.log("Disconnected from socket server");
      });

      socket.on("directMessage", (msg) => {
        console.log("Received message:", msg);
        const senderName = msg.sender === me.id ? "Me" : "Friend";
        setChatLog((prev) => [...prev, `${senderName}: ${msg.message}`]);
      });

      socket.on("connect_error", (error) => {
        console.error("Socket connection error:", error);
      });
    } else if (socket.connected) {
      socket.emit("register", { userId: me.id });
    }

    return () => {};
  }, [me?.id, you?.id]);

  React.useEffect(() => {
    if (!chatOpen) setChatLog([]);
  }, [chatOpen]);

  if (!chatOpen || !me || !you) return null;

  const sendMessage = () => {
    if (!message.trim() || !socket) {
      console.warn("Cannot send message: missing requirements", {
        hasMessage: !!message.trim(),
        hasSocket: !!socket,
      });
      return;
    }

    console.log("Sending message:", { from: me.id, to: you.id, message });

    socket.emit("directMessage", {
      from: me.id,
      to: you.id,
      message,
    });

    setChatLog((prev) => [...prev, `Me: ${message}`]);
    setMessage("");
  };

  return (
    <div className="w-full flex flex-col gap-2">
      <Textarea value={chatLog.join("\n")} disabled className="h-40 resize-none" />
      <div className="w-full flex flex-row gap-2">
        <Input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") sendMessage();
          }}
        />
        <Button onClick={sendMessage}>Send</Button>
      </div>
    </div>
  );
};
