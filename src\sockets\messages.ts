import "dotenv/config";
import { Server } from "socket.io";
import http from "http";
import { db } from "@/db";
import { conversation } from "@/db/schema";
import { sql } from "drizzle-orm";

const httpServer = http.createServer();
const port = Number(process.env.NEXT_PUBLIC_MESSAGE_SOCKET_PORT);

console.log("Socket.IO server starting on port:", port);
console.log("CORS origin:", process.env.NEXT_PUBLIC_HOST_URL);

const io = new Server(httpServer, {
  cors: {
    origin: process.env.NEXT_PUBLIC_HOST_URL,
    methods: ["GET", "POST"],
    credentials: true,
    allowedHeaders: ["*"],
  },
});

const userSocketMap = new Map<string, string>();

const messages: {
  from: string;
  to: string;
  message: string;
  timestamp: string;
}[] = [];

io.on("connection", (socket) => {
  console.log("New client:", socket.id);

  socket.on("register", ({ userId }) => {
    userSocketMap.set(userId, socket.id);
    console.log(`Registered user ${userId} -> ${socket.id}`);
  });

  socket.on("loadConversation", async ({ me, you }, callback) => {
    try {
      const result = await db
        .select()
        .from(conversation)
        .where(
          sql`users @> ARRAY[${me}, ${you}]::text[] 
                AND users @> ARRAY[${you}, ${me}]::text[]`
        )
        .orderBy(sql`id desc`)
        .limit(1);

      if (result.length > 0) {
        callback({ success: true, data: result[0].data });
      } else {
        callback({ success: true, data: [] }); // no history yet
      }
    } catch (err) {
      console.error("❌ Failed to load conversation:", err);
      callback({ success: false, error: "Database error" });
    }
  });

  socket.on("directMessage", ({ to, message, from }) => {
    const newMessage = {
      from,
      to,
      message,
      timestamp: new Date().toISOString(),
    };
    messages.push(newMessage);

    const targetSocketId = userSocketMap.get(to);
    if (targetSocketId) {
      io.to(targetSocketId).emit("directMessage", {
        sender: from,
        message,
      });
    }
  });

  socket.on("disconnect", async () => {
    for (const [userId, sockId] of userSocketMap.entries()) {
      if (sockId === socket.id) {
        userSocketMap.delete(userId);

        console.log(`User ${userId} disconnected`);
        console.log("Final Messages JSON:", JSON.stringify(messages, null, 2));

        if (messages.length > 0) {
          // grab unique participants
          const participants = Array.from(new Set(messages.flatMap((m) => [m.from, m.to])));

          try {
            await db.insert(conversation).values({
              users: participants,
              data: messages,
            });
            console.log("✅ Conversation saved to database");
          } catch (err) {
            console.error("❌ Failed to insert conversation:", err);
          }
        }

        break;
      }
    }
  });
});

httpServer.listen(port, () => {
  console.log(`Socket.IO running on :${port}`);
});
